# Local development environment variables
# This file is used for local development only and should not be committed to git

# PhonePe Configuration for Local Development
# Use production environment for testing with production credentials
PHONEPE_ENVIRONMENT=production
NEXT_PUBLIC_PHONEPE_ENVIRONMENT=production

# Production credentials for testing (these work on any domain for testing)
NEXT_PUBLIC_MERCHANT_ID=M11BWXEAW0AJ
NEXT_PUBLIC_SALT_KEY=63542457-2eb4-4ed4-83f2-da9eaed9fcca
NEXT_PUBLIC_SALT_INDEX=2

# Server-side production credentials
PHONEPE_PROD_MERCHANT_ID=M11BWXEAW0AJ
PHONEPE_PROD_SALT_KEY=63542457-2eb4-4ed4-83f2-da9eaed9fcca
PHONEPE_PROD_SALT_INDEX=2

# Application URL for local development

NEXT_PUBLIC_APP_URL=https://www.nibog.in


# Development mode
NODE_ENV=development

# WhatsApp Integration via Zaptra
WHATSAPP_NOTIFICATIONS_ENABLED=true
ZAPTRA_API_URL=https://demo.zaptra.in/api/wpbox
ZAPTRA_API_TOKEN=your_actual_zaptra_api_token_here
WHATSAPP_FALLBACK_ENABLED=true
WHATSAPP_RETRY_ATTEMPTS=3
WHATSAPP_TIMEOUT_MS=10000
WHATSAPP_DEBUG=true


