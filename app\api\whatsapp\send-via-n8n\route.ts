import { NextResponse } from 'next/server';
import { sendBookingConfirmationViaN8N } from '@/services/n8nWhatsAppService';
import { WhatsAppBookingData } from '@/services/whatsappService';

export async function POST(request: Request) {
  try {
    console.log('📱 N8N WhatsApp API route: Starting booking confirmation request');

    // Parse the request body
    const bookingData: WhatsAppBookingData = await request.json();
    console.log(`📱 N8N WhatsApp API route: Received booking data for ID: ${bookingData.bookingId}`);

    // Validate required fields
    const requiredFields = ['bookingId', 'parentName', 'parentPhone', 'childName', 'eventTitle'];
    for (const field of requiredFields) {
      if (!bookingData[field as keyof WhatsAppBookingData]) {
        return NextResponse.json(
          { 
            success: false, 
            error: `Missing required field: ${field}` 
          },
          { status: 400 }
        );
      }
    }

    // Send WhatsApp notification via N8N
    const result = await sendBookingConfirmationViaN8N(bookingData);

    if (result.success) {
      console.log(`✅ N8N WhatsApp API route: Message sent successfully for booking ${bookingData.bookingId}`);
      return NextResponse.json({
        success: true,
        message: 'WhatsApp notification sent via N8N successfully',
        n8nResponse: result.n8nResponse
      });
    } else {
      console.error(`❌ N8N WhatsApp API route: Failed to send message for booking ${bookingData.bookingId}: ${result.error}`);
      return NextResponse.json({
        success: false,
        error: result.error,
        n8nResponse: result.n8nResponse
      }, { status: 500 });
    }

  } catch (error) {
    console.error('📱 Error in N8N WhatsApp API route:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
