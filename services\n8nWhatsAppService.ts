/**
 * N8N WhatsApp Integration Service
 * Alternative to direct Zaptra API integration
 */

import { WhatsAppBookingData } from './whatsappService';

export interface N8NWhatsAppResponse {
  success: boolean;
  error?: string;
  n8nResponse?: any;
}

/**
 * Send booking confirmation via N8N webhook
 */
export async function sendBookingConfirmationViaN8N(
  bookingData: WhatsAppBookingData
): Promise<N8NWhatsAppResponse> {
  try {
    const n8nWebhookUrl = process.env.N8N_BOOKING_WEBHOOK_URL;
    
    if (!n8nWebhookUrl || n8nWebhookUrl.includes('your-actual-n8n-instance')) {
      return {
        success: false,
        error: 'N8N webhook URL not configured'
      };
    }

    console.log('📱 Sending booking data to N8N webhook...');
    console.log(`📱 N8N URL: ${n8nWebhookUrl}`);
    console.log(`📱 Booking ID: ${bookingData.bookingId}`);

    const response = await fetch(n8nWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        parentPhone: bookingData.parentPhone,
        parentName: bookingData.parentName,
        childName: bookingData.childName,
        eventTitle: bookingData.eventTitle,
        eventDate: bookingData.eventDate,
        eventVenue: bookingData.eventVenue,
        bookingRef: bookingData.bookingRef || `B${String(bookingData.bookingId).padStart(7, '0')}`,
        totalAmount: bookingData.totalAmount,
        paymentMethod: bookingData.paymentMethod,
        transactionId: bookingData.transactionId,
        bookingId: bookingData.bookingId
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('📱 N8N webhook error:', errorText);
      return {
        success: false,
        error: `N8N webhook failed: ${response.status}`,
        n8nResponse: errorText
      };
    }

    const result = await response.json();
    console.log('✅ N8N webhook successful:', result);

    return {
      success: true,
      n8nResponse: result
    };

  } catch (error) {
    console.error('📱 Error in N8N WhatsApp service:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
